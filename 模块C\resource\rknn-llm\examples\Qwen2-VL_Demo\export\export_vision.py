import numpy as np
import os
import torch
from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer
import torch.nn.functional as F
import argparse
import gc

argparse = argparse.ArgumentParser()
argparse.add_argument('--step', type=int, help='export step', required=True)
argparse.add_argument('--path', type=str, default='Qwen/Qwen2-VL-2B-Instruct', help='model path', required=False)
argparse.add_argument('--batch', type=int, default=1, help='batch size', required=False)
argparse.add_argument('--height', type=int, default=392, help='image height', required=False)
argparse.add_argument('--width', type=int, default=392, help='image width', required=False)
argparse.add_argument('--savepath', type=str, default='qwen2-vl-2b/qwen2_vl_2b_vision.onnx', help='save path', required=False)
argparse.add_argument('--device', type=str, default='auto', help='device to use: auto, cpu, cuda', required=False)
args = argparse.parse_args()

step = args.step

# 设备选择和内存管理
def get_device():
    if args.device == 'auto':
        if torch.cuda.is_available():
            # 检查GPU显存
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"GPU memory: {gpu_memory:.1f}GB")
            if gpu_memory < 20:  # 如果显存小于20GB，使用CPU
                print("GPU memory insufficient, using CPU")
                return 'cpu'
            return 'cuda'
        else:
            return 'cpu'
    return args.device

device = get_device()
print(f"Using device: {device}")

# 清理GPU缓存
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    gc.collect()

# 加载本地模型
path = args.path
print(f"Loading model from {path}...")

# 根据设备选择加载策略
if device == 'cpu':
    model = Qwen2VLForConditionalGeneration.from_pretrained(
        path,
        torch_dtype=torch.float32,
        low_cpu_mem_usage=True,
        trust_remote_code=True,
        device_map='cpu').eval()
else:
    model = Qwen2VLForConditionalGeneration.from_pretrained(
        path,
        torch_dtype=torch.float32,
        low_cpu_mem_usage=True,
        trust_remote_code=True,
        device_map='auto').eval()

tokenizer = AutoTokenizer.from_pretrained(path, trust_remote_code=True, use_fast=False)
print("Model loaded successfully")

N = args.batch                           # batch size
channel = 3                                 # 3 for RGB
H = args.height                         # image height, must be divisible by (merge_size * patch_size)
W = args.width                          # image width, must be divisible by (merge_size * patch_size)
merge_size = 2
temporal_patch_size = 2
patch_size = 14
grid_t = N // temporal_patch_size if N%temporal_patch_size == 0 else N // temporal_patch_size + 1
grid_h = H // patch_size
grid_w = W // patch_size

def export_onnx(image):
    # 内存优化：使用with torch.no_grad()减少内存使用
    with torch.no_grad():
        if N == 1:
            images = image.repeat(temporal_patch_size, 1, 1, 1)
        elif N % temporal_patch_size != 0:
            repeat_time = temporal_patch_size - N % temporal_patch_size
            repeat_image = image[-1:, ...].repeat(repeat_time, 1, 1, 1)
            images = torch.cat((image, repeat_image), dim=0)

        # 确保数据在正确的设备上
        images = images.to(device)

        patches = images.reshape(grid_t, temporal_patch_size, channel, grid_h//merge_size, merge_size, patch_size, grid_w//merge_size, merge_size, patch_size)
        patches = patches.permute(0, 3, 6, 4, 7, 2, 1, 5, 8)
        flatten_patches = patches.reshape(grid_t * grid_h * grid_w, channel * temporal_patch_size * patch_size * patch_size)

        # 清理中间变量
        del images, patches
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        model.visual.forward = forward_new(model.visual)
        if step == 1:
            grid_tensor = torch.tensor([grid_t, grid_h, grid_w], device=device).unsqueeze(0)
            feature = model.visual(flatten_patches, grid_tensor)
        else:
            feature = model.visual(flatten_patches)
        return feature

def forward_new(self):
    def tmp (hidden_states, grid_thw=None):
        hidden_states = self.patch_embed(hidden_states)
        if grid_thw is not None:
            rotary_pos_emb = self.rot_pos_emb(grid_thw)
            cu_seqlens = torch.repeat_interleave(grid_thw[:, 1] * grid_thw[:, 2], grid_thw[:, 0]).cumsum(
                dim=0, dtype=torch.int32
            )
            cu_seqlens = F.pad(cu_seqlens, (1, 0), value=0)
            np.save("./rotary_pos_emb.npy", rotary_pos_emb.cpu().detach().numpy())
            np.save("./cu_seqlens.npy", cu_seqlens.cpu().detach().numpy())
        else:
            rotary_pos_emb = torch.from_numpy(np.load("./rotary_pos_emb.npy")).to(dtype=hidden_states.dtype, device=hidden_states.device)
            cu_seqlens = torch.from_numpy(np.load("./cu_seqlens.npy")).to(dtype=torch.int32, device=hidden_states.device)
        
        for blk in self.blocks:
            hidden_states = blk(hidden_states, cu_seqlens=cu_seqlens, rotary_pos_emb=rotary_pos_emb)

        return self.merger(hidden_states)
    return tmp

# 导出 Vison 部分所对应的 onnx 模型，假设输入是2x3x392x392->(28x28)x(3x2x14x14)
print(f"Creating input tensor with shape: {N}x{channel}x{H}x{W}")
pixel_values = torch.randn(N, channel, H, W, device=device, dtype=torch.float32)

model.forward = export_onnx
model = model.to(device).to(torch.float32).eval()

# 清理缓存
if torch.cuda.is_available():
    torch.cuda.empty_cache()
gc.collect()

try:
    if step == 1:
        print("==========================================================")
        print("Generating the rotary_pos_emb and cu_seqlens...")
        with torch.no_grad():
            feature = model(pixel_values)
        print("Generating the rotary_pos_emb and cu_seqlens done.")
    else:
        print("==========================================================")
        print(f"Exporting the vision part of {path} to onnx format.")
        os.makedirs(os.path.dirname(args.savepath), exist_ok=True)

        # 使用更保守的ONNX导出设置
        torch.onnx.export(
            model,
            pixel_values,
            args.savepath,
            opset_version=18,
            do_constant_folding=True,
            input_names=['pixel_values'],
            output_names=['features'],
            dynamic_axes={
                'pixel_values': {0: 'batch_size'},
                'features': {0: 'batch_size'}
            }
        )
        print(f"ONNX model saved to {args.savepath}")

except RuntimeError as e:
    if "out of memory" in str(e).lower():
        print(f"GPU out of memory error: {e}")
        print("Try using --device cpu or reduce batch size/image dimensions")
    else:
        print(f"Runtime error: {e}")
    raise
except Exception as e:
    print(f"Unexpected error: {e}")
    raise
finally:
    # 清理资源
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()

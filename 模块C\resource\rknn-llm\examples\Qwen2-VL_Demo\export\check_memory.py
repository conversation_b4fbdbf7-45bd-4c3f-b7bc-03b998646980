#!/usr/bin/env python3
"""
内存和GPU使用情况检查脚本
用于诊断export_vision.py的内存问题
"""

import torch
import psutil
import subprocess
import sys
import os

def check_system_memory():
    """检查系统内存使用情况"""
    memory = psutil.virtual_memory()
    swap = psutil.swap_memory()
    
    print("=== 系统内存状态 ===")
    print(f"总内存: {memory.total / 1024**3:.1f} GB")
    print(f"可用内存: {memory.available / 1024**3:.1f} GB")
    print(f"已用内存: {memory.used / 1024**3:.1f} GB ({memory.percent:.1f}%)")
    print(f"交换分区总计: {swap.total / 1024**3:.1f} GB")
    print(f"交换分区已用: {swap.used / 1024**3:.1f} GB ({swap.percent:.1f}%)")
    
    if memory.percent > 80:
        print("⚠️  警告: 系统内存使用率过高!")
    if swap.percent > 50:
        print("⚠️  警告: 交换分区使用率过高!")

def check_gpu_memory():
    """检查GPU内存使用情况"""
    if not torch.cuda.is_available():
        print("=== GPU状态 ===")
        print("CUDA不可用")
        return
    
    print("=== GPU状态 ===")
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        allocated = torch.cuda.memory_allocated(i) / 1024**3
        reserved = torch.cuda.memory_reserved(i) / 1024**3
        total = props.total_memory / 1024**3
        
        print(f"GPU {i}: {props.name}")
        print(f"  总显存: {total:.1f} GB")
        print(f"  已分配: {allocated:.1f} GB")
        print(f"  已保留: {reserved:.1f} GB")
        print(f"  可用: {total - reserved:.1f} GB")
        
        if reserved / total > 0.8:
            print(f"⚠️  警告: GPU {i} 显存使用率过高!")

def check_processes():
    """检查占用GPU的进程"""
    try:
        result = subprocess.run(['nvidia-smi', '--query-compute-apps=pid,process_name,used_memory', '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            print("=== GPU进程 ===")
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if line.strip():
                    pid, name, memory = line.split(', ')
                    print(f"PID {pid}: {name} - {memory} MB")
        else:
            print("=== GPU进程 ===")
            print("没有发现GPU进程")
    except FileNotFoundError:
        print("nvidia-smi 命令不可用")

def estimate_model_memory():
    """估算模型内存需求"""
    print("=== 模型内存估算 ===")
    
    # Qwen2-VL-2B模型大致参数
    model_params = 2e9  # 2B参数
    bytes_per_param = 4  # float32
    model_memory_gb = (model_params * bytes_per_param) / 1024**3
    
    # 考虑激活值、梯度等额外开销
    total_memory_gb = model_memory_gb * 3  # 经验值：模型权重的3倍
    
    print(f"模型权重估算: {model_memory_gb:.1f} GB")
    print(f"总内存需求估算: {total_memory_gb:.1f} GB")
    
    # 检查是否满足要求
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        if gpu_memory < total_memory_gb:
            print(f"⚠️  警告: GPU显存可能不足! 需要 {total_memory_gb:.1f} GB, 可用 {gpu_memory:.1f} GB")
    
    system_memory = psutil.virtual_memory().available / 1024**3
    if system_memory < total_memory_gb:
        print(f"⚠️  警告: 系统内存可能不足! 需要 {total_memory_gb:.1f} GB, 可用 {system_memory:.1f} GB")

def suggest_solutions():
    """提供解决方案建议"""
    print("\n=== 解决方案建议 ===")
    
    memory = psutil.virtual_memory()
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        
        if gpu_memory < 20:
            print("1. 使用CPU模式: python export_vision.py --step 1 --device cpu")
        
        if memory.available / 1024**3 < 16:
            print("2. 关闭其他程序释放内存")
            print("3. 增加交换分区大小")
    
    print("4. 减小batch size: --batch 1")
    print("5. 减小图像尺寸: --height 224 --width 224")
    print("6. 清理GPU进程: sudo pkill -f python")

def main():
    print("内存和GPU使用情况检查")
    print("=" * 50)
    
    check_system_memory()
    print()
    check_gpu_memory()
    print()
    check_processes()
    print()
    estimate_model_memory()
    suggest_solutions()

if __name__ == "__main__":
    main()

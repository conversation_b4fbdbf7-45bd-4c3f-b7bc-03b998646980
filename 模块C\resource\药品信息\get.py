import os
import json

def get_folders_to_json(directory_path, output_json_path):
    """
    读取指定目录下的所有文件夹名字，保存为JSON文件。

    Args:
        directory_path (str): 要读取的目录路径。
        output_json_path (str): 输出JSON文件的路径。
    """
    try:
        # 检查目录是否存在
        if not os.path.exists(directory_path):
            print(f"错误：指定的目录 '{directory_path}' 不存在。")
            return

        # 检查目录是否为一个目录
        if not os.path.isdir(directory_path):
            print(f"错误：'{directory_path}' 不是一个有效的目录。")
            return

        # 获取目录下的所有条目
        entries = os.listdir(directory_path)
        # 筛选出其中的文件夹
        folder_list = [entry for entry in entries if os.path.isdir(os.path.join(directory_path, entry))]

        # 将列表写入JSON文件
        with open(output_json_path, 'w', encoding='utf-8') as json_file:
            json.dump(folder_list, json_file, ensure_ascii=False, indent=4)

        print(f"成功：已将 '{directory_path}' 目录下的 {len(folder_list)} 个文件夹名字保存到 '{output_json_path}'。")
        print(f"文件夹列表：{folder_list}")

    except PermissionError:
        print(f"错误：没有权限访问目录 '{directory_path}' 或无法写入文件 '{output_json_path}'。")
    except Exception as e:
        print(f"发生错误：{e}")

# --- 使用示例 ---
if __name__ == "__main__":
    # 设置要读取的目录路径 (请修改为你需要的路径)
    target_directory = "."  # "." 表示当前脚本所在的目录
    # 设置输出的JSON文件路径
    output_file = "folders_list.json"  # JSON文件将保存在脚本所在目录

    # 调用函数执行操作
    get_folders_to_json(target_directory, output_file)

    # 如果你想读取其他目录，例如 D:\MyProjects，可以这样写：
    # get_folders_to_json("D:\\MyProjects", "D:\\MyProjects\\folders.json")
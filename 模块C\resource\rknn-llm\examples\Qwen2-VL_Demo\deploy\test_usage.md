# Qwen2-VL Demo 使用说明（持续交互版本）

## 新功能特点

修改后的 demo 实现了**持续交互模式**：
- **一次加载模型**：程序启动时加载模型，避免重复加载开销
- **持续交互**：可以连续输入多个图片和文本进行推理
- **动态图片加载**：每次输入时动态加载不同的图片
- **灵活退出**：支持 Ctrl+C 或输入 "exit" 退出

## 使用方式

### 启动程序
```bash
./demo encoder_model_path llm_model_path max_new_tokens max_context_len rknn_core_num
```

### 参数说明
1. `encoder_model_path`: 视觉编码器模型路径（.rknn文件）
2. `llm_model_path`: 语言模型路径（.rkllm文件）
3. `max_new_tokens`: 最大生成token数（加载时设置）
4. `max_context_len`: 最大上下文长度（加载时设置）
5. `rknn_core_num`: RKNN核心数（加载时设置）

### 启动示例
```bash
./demo models/qwen2-vl-vision_rk3588.rknn models/qwen2-vl-llm_rk3588.rkllm 128 512 3
```

## 交互使用

### 输入格式
```
<image_path> <text_prompt>
```

### 使用示例
```bash
# 启动程序后的交互示例：
user: demo.jpg What is in this image?
user: photo1.jpg 请描述这张图片的内容
user: image2.png Analyze the objects in this picture
user: exit
```

### 错误处理
- 如果图片路径不存在，会显示错误信息并继续等待下一个输入
- 如果输入格式不正确，会显示格式提示
- 如果图片编码失败，会显示错误信息并继续

## 优势

1. **性能优化**：模型只加载一次，避免重复初始化开销
2. **使用便捷**：可以快速测试多张图片，无需重启程序
3. **资源高效**：减少内存和时间消耗
4. **错误恢复**：单次错误不会导致程序退出

## 退出方式

- 输入 `exit`
- 按 `Ctrl+C`

## 注意事项

- 图片路径可以是相对路径或绝对路径
- 文本提示支持中英文
- max_context_len 必须大于 text-token-num + image-token-num + max_new_tokens
- 对于 Qwen2-VL-2B，EMBED_SIZE=1536；对于 Qwen2-VL-7B，EMBED_SIZE=3584

## 重要修复说明

**关于 `<image>` 标记的处理**：
- 修复了原版本中当文本提示改变时模型无法正确识别图片的问题
- 如果你的文本提示中**不包含** `<image>` 标记，程序会自动在开头添加
- 如果你的文本提示中**已包含** `<image>` 标记，程序会直接使用
- 这确保了模型能正确理解图片和文本的关系

**推荐的输入格式**：
```bash
# 方式1：不包含<image>标记（推荐，程序会自动添加）
user: demo.jpg What is in this image?

# 方式2：手动包含<image>标记
user: demo.jpg <image>What is in this image?
```

## 编译

使用编译脚本：
```bash
cd deploy
./build-linux.sh
```

编译完成后，可执行文件位于 `install/demo_Linux_aarch64/demo`
